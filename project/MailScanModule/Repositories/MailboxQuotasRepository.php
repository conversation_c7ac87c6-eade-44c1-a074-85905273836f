<?php

declare(strict_types=1);

namespace MailScanModule\Repositories;

use MailScanModule\Entities\MailboxQuotas;
use OrmModule\Repositories\DoctrineRepository_Abstract;

class MailboxQuotasRepository extends DoctrineRepository_Abstract
{
    public function getMailboxQuotasByServiceId(?int $serviceId): ?MailboxQuotas
    {
        if ($serviceId === null) {
            return null;
        }

        return $this->findOneBy(['serviceId' => $serviceId]);
    }

    /**
     * @return MailboxQuotas[]
     */
    public function getCompanyQuotas(int $companyId): array
    {
        return $this->findBy(['companyId' => $companyId]);
    }

    /**
     * @throws \Exception
     */
    public function persistQuota(MailboxQuotas $entity): void
    {
        $this->getEntityManager()->persist($entity);
        $this->getEntityManager()->flush();
    }
}