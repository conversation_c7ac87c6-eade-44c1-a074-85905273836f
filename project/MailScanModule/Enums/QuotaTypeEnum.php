<?php

declare(strict_types=1);

namespace MailScanModule\Enums;

enum QuotaTypeEnum: string
{
    case STATUTORY_COLLECTED = 'statutory_collected';
    case STATUTORY_POSTED = 'statutory_posted';
    case NON_STATUTORY_COLLECTED = 'non_statutory_collected';
    case NON_STATUTORY_POSTED = 'non_statutory_posted';
    case PARCEL_COLLECTED = 'parcel_collected';
    case PARCEL_POSTED = 'parcel_posted';
    case MAIL_COLLECTED = 'mail_collected';
    case MAIL_POSTED = 'mail_posted';

    public static function fromTypeAndHandlingOption(string $type, string $handlingOption): self
    {
        return match ([$type, $handlingOption]) {
            [PostItemTypeEnum::TYPE_STATUTORY->value, 'scanned'],
            [PostItemTypeEnum::TYPE_STATUTORY->value, 'collect'] => self::STATUTORY_COLLECTED,
            [PostItemTypeEnum::TYPE_STATUTORY->value, 'post'] => self::STATUTORY_POSTED,
            [PostItemTypeEnum::TYPE_NON_STATUTORY->value, 'scanned'],
            [PostItemTypeEnum::TYPE_NON_STATUTORY->value, 'collect'] => self::NON_STATUTORY_COLLECTED,
            [PostItemTypeEnum::TYPE_NON_STATUTORY->value, 'post'] => self::NON_STATUTORY_POSTED,
            [PostItemTypeEnum::TYPE_PARCEL->value, 'collect'] => self::PARCEL_COLLECTED,
            [PostItemTypeEnum::TYPE_PARCEL->value, 'post'] => self::PARCEL_POSTED,
            default => throw new \InvalidArgumentException("Invalid type '{$type}' and handling option '{$handlingOption}' combination"),
        };
    }

    public static function fromGenericHandlingOption(string $handlingOption): self
    {
        return match ($handlingOption) {
            'scanned', 'collect' => self::MAIL_COLLECTED,
            'post' => self::MAIL_POSTED,
            default => throw new \InvalidArgumentException("Invalid generic handling option '{$handlingOption}'"),
        };
    }

    public function getPostItemType(): ?PostItemTypeEnum
    {
        return match ($this) {
            self::STATUTORY_COLLECTED, self::STATUTORY_POSTED => PostItemTypeEnum::TYPE_STATUTORY,
            self::NON_STATUTORY_COLLECTED, self::NON_STATUTORY_POSTED => PostItemTypeEnum::TYPE_NON_STATUTORY,
            self::PARCEL_COLLECTED, self::PARCEL_POSTED => PostItemTypeEnum::TYPE_PARCEL,
            self::MAIL_COLLECTED, self::MAIL_POSTED => null, // Generic mail quotas don't have a specific type
        };
    }

    public function getHandlingOption(): string
    {
        return match ($this) {
            self::STATUTORY_COLLECTED, self::NON_STATUTORY_COLLECTED, self::PARCEL_COLLECTED, self::MAIL_COLLECTED => 'collect',
            self::STATUTORY_POSTED, self::NON_STATUTORY_POSTED, self::PARCEL_POSTED, self::MAIL_POSTED => 'post',
        };
    }

    public function isGeneric(): bool
    {
        return in_array($this, [self::MAIL_COLLECTED, self::MAIL_POSTED], true);
    }
}
