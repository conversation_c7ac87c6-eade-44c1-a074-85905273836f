<?php

declare(strict_types=1);

namespace MailScanModule\Entities;

use DateTime;
use Gedmo\Mapping\Annotation as Gedmo;

/**
 * @Orm\Entity(repositoryClass = "MailScanModule\Repositories\MailboxQuotasRepository")
 * @Gedmo\Loggable(logEntryClass="LoggableModule\Entities\LogEntry")
 * @Orm\Table(name="cms2_mailbox_quotas")
 */
class MailboxQuotas implements \JsonSerializable
{
    /**
     * @Orm\Column(type="integer", name="mailboxQuotasId")
     * @Orm\Id
     * @Orm\GeneratedValue(strategy="AUTO")
     */
    private int $mailboxQuotasId;

    /**
     * @Orm\Column(type="integer", name="companyId")
     */
    private int $companyId;

    /**
     * @Orm\Column(type="integer", name="serviceId")
     */
    private int $serviceId;

    /**
     * @Orm\Column(type="integer", name="statutoryCollected")
     */
    private int $statutoryCollected;

    /**
     * @Orm\Column(type="integer", name="statutoryPosted")
     */
    private int $statutoryPosted;

    /**
     * @Orm\Column(type="integer", name="nonStatutoryCollected")
     */
    private int $nonStatutoryCollected;

    /**
     * @Orm\Column(type="integer", name="nonStatutoryPosted")
     */
    private int $nonStatutoryPosted;

    /**
     * @Orm\Column(type="integer", name="parcelCollected")
     */
    private int $parcelCollected;

    /**
     * @Orm\Column(type="integer", name="parcelPosted")
     */
    private int $parcelPosted;

    /**
     * @Orm\Column(type="integer", name="mailCollected")
     */
    private int $mailCollected;

    /**
     * @Orm\Column(type="integer", name="mailPosted")
     */
    private int $mailPosted;

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     */
    private DateTime $dtExpires;

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     * @Gedmo\Timestampable(on="create")
     */
    private DateTime $dtc;

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     * @Gedmo\Timestampable(on="create", on="update")
     */
    private DateTime $dtm;

    public function getMailboxQuotasId(): int
    {
        return $this->mailboxQuotasId;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getServiceId(): int
    {
        return $this->serviceId;
    }

    public function getStatutoryCollected(): int
    {
        return $this->statutoryCollected;
    }

    public function getStatutoryPosted(): int
    {
        return $this->statutoryPosted;
    }

    public function getNonStatutoryCollected(): int
    {
        return $this->nonStatutoryCollected;
    }

    public function getNonStatutoryPosted(): int
    {
        return $this->nonStatutoryPosted;
    }

    public function getParcelCollected(): int
    {
        return $this->parcelCollected;
    }

    public function getParcelPosted(): int
    {
        return $this->parcelPosted;
    }

    public function getMailCollected(): int
    {
        return $this->mailCollected;
    }

    public function getMailPosted(): int
    {
        return $this->mailPosted;
    }

    public function getDtExpires(): DateTime
    {
        return $this->dtExpires;
    }

    public function getDtc(): DateTime
    {
        return $this->dtc;
    }

    public function getDtm(): DateTime
    {
        return $this->dtm;
    }

    public function setCompanyId(int $companyId): self
    {
        $this->companyId = $companyId;
        return $this;
    }

    public function setServiceId(int $serviceId): self
    {
        $this->serviceId = $serviceId;
        return $this;
    }

    public function setStatutoryCollected(int $statutoryCollected): self
    {
        $this->statutoryCollected = $statutoryCollected;
        return $this;
    }

    public function setStatutoryPosted(int $statutoryPosted): self
    {
        $this->statutoryPosted = $statutoryPosted;
        return $this;
    }

    public function setNonStatutoryCollected(int $nonStatutoryCollected): self
    {
        $this->nonStatutoryCollected = $nonStatutoryCollected;
        return $this;
    }

    public function setNonStatutoryPosted(int $nonStatutoryPosted): self
    {
        $this->nonStatutoryPosted = $nonStatutoryPosted;
        return $this;
    }

    public function setParcelCollected(int $parcelCollected): self
    {
        $this->parcelCollected = $parcelCollected;
        return $this;
    }

    public function setParcelPosted(int $parcelPosted): self
    {
        $this->parcelPosted = $parcelPosted;
        return $this;
    }

    public function setMailCollected(int $mailCollected): self
    {
        $this->mailCollected = $mailCollected;
        return $this;
    }

    public function setMailPosted(int $mailPosted): self
    {
        $this->mailPosted = $mailPosted;
        return $this;
    }

    public function setDtExpires(DateTime $dtExpires): self
    {
        $this->dtExpires = $dtExpires;
        return $this;
    }


    public function jsonSerialize(): array
    {
        return [
            'mailboxQuotasId' => $this->mailboxQuotasId,
            'companyId' => $this->companyId,
            'serviceId' => $this->serviceId,
            'statutoryCollected' => $this->statutoryCollected,
            'statutoryPosted' => $this->statutoryPosted,
            'nonStatutoryCollected' => $this->nonStatutoryCollected,
            'nonStatutoryPosted' => $this->nonStatutoryPosted,
            'parcelCollected' => $this->parcelCollected,
            'parcelPosted' => $this->parcelPosted,
            'mailCollected' => $this->mailCollected,
            'mailPosted' => $this->mailPosted,
            'dtExpires' => $this->dtExpires,
            'dtc' => $this->dtc,
            'dtm' => $this->dtm,
        ];
    }
}