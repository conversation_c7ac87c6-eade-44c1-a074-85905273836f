<?php

declare(strict_types=1);

namespace MailScanModule\Entities;

use DateTime;
use Gedmo\Mapping\Annotation as Gedmo;
use MailScanModule\Enums\QuotaTypeEnum;

/**
 * @Orm\Entity
 * @Gedmo\Loggable(logEntryClass="LoggableModule\Entities\LogEntry")
 * @Orm\Table(name="cms2_mailbox_quota_entries")
 */
class MailboxQuotaEntry implements \JsonSerializable
{
    /**
     * @Orm\Column(type="integer", name="mailboxQuotaEntryId")
     * @Orm\Id
     * @Orm\GeneratedValue(strategy="AUTO")
     */
    private int $mailboxQuotaEntryId;

    /**
     * @Orm\ManyToOne(targetEntity="MailboxQuotas", inversedBy="quotaEntries")
     * @Orm\JoinColumn(name="mailboxQuotasId", referencedColumnName="mailboxQuotasId")
     */
    private MailboxQuotas $mailboxQuotas;

    /**
     * @Orm\Column(type="string", name="quotaType", enumType="MailScanModule\Enums\QuotaTypeEnum")
     */
    private QuotaTypeEnum $quotaType;

    /**
     * @Orm\Column(type="integer", name="usageCount")
     */
    private int $usageCount;

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     * @Gedmo\Timestampable(on="create")
     */
    private DateTime $dtc;

    /**
     * @var DateTime
     * @Orm\Column(type="datetime")
     * @Gedmo\Timestampable(on="create", on="update")
     */
    private DateTime $dtm;

    public function __construct(MailboxQuotas $mailboxQuotas, QuotaTypeEnum $quotaType, int $usageCount = 0)
    {
        $this->mailboxQuotas = $mailboxQuotas;
        $this->quotaType = $quotaType;
        $this->usageCount = $usageCount;
    }

    public function getMailboxQuotaEntryId(): int
    {
        return $this->mailboxQuotaEntryId;
    }

    public function getMailboxQuotas(): MailboxQuotas
    {
        return $this->mailboxQuotas;
    }

    public function getQuotaType(): QuotaTypeEnum
    {
        return $this->quotaType;
    }

    public function getUsageCount(): int
    {
        return $this->usageCount;
    }

    public function setUsageCount(int $usageCount): self
    {
        $this->usageCount = $usageCount;
        return $this;
    }

    public function addUsage(int $amount = 1): self
    {
        $this->usageCount += $amount;
        return $this;
    }

    public function getDtc(): DateTime
    {
        return $this->dtc;
    }

    public function getDtm(): DateTime
    {
        return $this->dtm;
    }

    public function jsonSerialize(): array
    {
        return [
            'mailboxQuotaEntryId' => $this->mailboxQuotaEntryId,
            'quotaType' => $this->quotaType->value,
            'usageCount' => $this->usageCount,
            'dtc' => $this->dtc,
            'dtm' => $this->dtm,
        ];
    }
}
