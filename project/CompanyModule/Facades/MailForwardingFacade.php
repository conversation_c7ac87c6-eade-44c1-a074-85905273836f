<?php

declare(strict_types=1);

namespace CompanyModule\Facades;

use CompanyModule\Entities\Settings\MailForwardingAddressSetting;
use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use CompanyModule\Repositories\CompanySettingsRepository;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use MailScanModule\Entities\MailboxQuotas;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Exceptions\NoMailboxQuotaAvailable;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Repositories\MailboxQuotasRepository;


class MailForwardingFacade
{
    public function __construct(
        private readonly CompanySettingsRepository $companySettingsRepository,
        private readonly MailboxQuotasRepository $mailboxQuotasRepository,
    ) {
    }

    /**
     *
     * @throws \InvalidArgumentException
     * @throws NoMailboxQuotaAvailable
     */
    public function getQuotasByType(Company $company, string $handlingOption, string $type = null): ?int
    {
        $quota = $this->getMailboxQuota($company);

        if (!empty($type)) {
            return $this->getItemQuotaByTypeAndHandlingOption($type, $handlingOption, $quota);
        }

        return $this->getGenericQuotaByHandlingOption($handlingOption, $quota);
    }

    /**
     * @throws \InvalidArgumentException
     * @throws NoMailboxQuotaAvailable
     * @throws \Exception
     */
    public function addQuotas(Company $company, int $amount, string $type, string $handlingOption, bool $persist = true): int
    {
        $quota = $this->getMailboxQuota($company);

        $quotaAmount = $this->addQuotasByTypeAndHandlingOption($type, $handlingOption, $quota, $amount);

        if ($persist) {
            $this->mailboxQuotasRepository->persistQuota($quota);
        }

        return $quotaAmount;
    }

    /**
     * @throws \InvalidArgumentException
     * @throws NoMailboxQuotaAvailable
     * @throws \Exception
     */
    public function addGenericQuotas(Company $company, string $handlingOption, int $amount = 1, bool $persist = true): int
    {
        $quota = $this->getMailboxQuota($company);

        $quotaAmount = $this->addGenericQuotaByHandlingOption($handlingOption, $quota, $amount);

        if ($persist) {
            $this->mailboxQuotasRepository->persistQuota($quota);
        }

        return $quotaAmount;
    }

    /**
     * @throws NoMailboxQuotaAvailable
     * @throws \Exception
     */
    public function updateQuotas(Company $company, int $amount, string $type, string $handlingOption, bool $generic = false): int
    {
        $quota = $this->getMailboxQuota($company);

        $quotaAmount = $generic
            ? $this->updateGenericQuotasByHandlingOption($handlingOption, $quota, $amount)
            : $this->updateQuotasByTypeAndHandlingOption($type, $handlingOption, $quota, $amount);

        $this->mailboxQuotasRepository->persistQuota($quota);

        return $quotaAmount;
    }

    /**
     * @deprecated use MailScanModule\Facades\MailForwardingAddressFacade::getMailForwardingAddress
     * @throws NonUniqueResultException
     */
    public function getMailForwardingAddress(Company $company, bool $allowNullSetting = true): MailForwardingAddressSetting
    {
        /* @var MailForwardingAddressSetting|null $setting */
        $setting = $this->companySettingsRepository->getSettingByClass($company, MailForwardingAddressSetting::class);

        if (empty($setting)) {
            return $allowNullSetting
                ? new MailForwardingAddressSetting($company)
                : new MailForwardingAddressSetting(
                    $company,
                    $company->getPremise(),
                    $company->getStreet(),
                    $company->getThoroughfare(),
                    $company->getPostTown(),
                    $company->getCountry(),
                    $company->getPostcode()
                );
        }

        return $setting; /** @phpstan-ignore-line */
    }

    /**
     * @deprecated use CompanyModule\Facades\PostItemHandlingFacade::setHandlingSettingByType instead
     */
    public function setPostItemHandlingSetting(PostItemHandlingSetting $settings): void
    {
        $this->companySettingsRepository->persist($settings);
        $this->companySettingsRepository->flush();
    }

    /**
     * @throws NoMailboxQuotaAvailable
     */
    private function getMailboxQuota(Company $company): MailboxQuotas
    {
        return $this->mailboxQuotasRepository->getMailboxQuotasByServiceId($company->getActiveMailboxService()->getServiceId())
            ?? throw new NoMailboxQuotaAvailable($company->getCompanyId());
    }

    public function clearDoctrineMemory(): void
    {
        $this->companySettingsRepository->getEntityManager()->clear();
        $this->mailboxQuotasRepository->getEntityManager()->clear();
    }

    private function getStatutoryQuotaByHandlingOption(string $handlingOption, MailboxQuotas &$quota): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->getStatutoryCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->getStatutoryPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when getting statutory quotas'),
        };
    }

    private function getNonStatutoryQuotaByHandlingOption(string $handlingOption, MailboxQuotas &$quota): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->getNonStatutoryCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->getNonStatutoryPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when getting non-statutory quotas'),
        };
    }

    private function getParcelQuotaByHandlingOption(string $handlingOption, MailboxQuotas &$quota): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->getParcelCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->getParcelPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when getting parcel quotas'),
        };
    }

    private function getItemQuotaByTypeAndHandlingOption(string $type, string $handlingOption, MailboxQuotas $quota): int
    {
        return match (PostItemTypeEnum::from($type)) {
            PostItemTypeEnum::TYPE_STATUTORY => $this->getStatutoryQuotaByHandlingOption($handlingOption, $quota),
            PostItemTypeEnum::TYPE_NON_STATUTORY => $this->getNonStatutoryQuotaByHandlingOption($handlingOption, $quota),
            PostItemTypeEnum::TYPE_PARCEL => $this->getParcelQuotaByHandlingOption($handlingOption, $quota),
            default => throw new \InvalidArgumentException('Invalid post item type when getting quotas'),
        };
    }

    private function getGenericQuotaByHandlingOption(string $handlingOption, MailboxQuotas &$quota): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->getMailCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->getMailPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when getting generic quotas'),
        };
    }

    private function addStatutoryQuotaByHandlingOption(string $handlingOption, MailboxQuotas &$quota, int $amount = 1): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->setStatutoryCollected($quota->getStatutoryCollected() + $amount)
                ->getStatutoryCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->setStatutoryPosted($quota->getStatutoryPosted() + $amount)
                ->getStatutoryPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when adding statutory quotas'),
        };
    }

    private function addNonStatutoryQuotaByHandlingOption(string $handlingOption, MailboxQuotas &$quota, int $amount = 1): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->setNonStatutoryCollected($quota->getNonStatutoryCollected() + $amount)
                ->getNonStatutoryCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->setNonStatutoryPosted($quota->getNonStatutoryPosted() + $amount)
                ->getNonStatutoryPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when adding non-statutory quotas'),
        };
    }

    private function addParcelQuotaByHandlingOption(string $handlingOption, MailboxQuotas &$quota, int $amount = 1): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->setParcelCollected($quota->getParcelCollected() + $amount)
                ->getParcelCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->setParcelPosted($quota->getParcelPosted() + $amount)
                ->getParcelPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when adding parcel quotas'),
        };
    }

    private function addQuotasByTypeAndHandlingOption(string $type, string $handlingOption, MailboxQuotas $quota, int $amount): int
    {
        return match (PostItemTypeEnum::from($type)) {
            PostItemTypeEnum::TYPE_STATUTORY => $this->addStatutoryQuotaByHandlingOption($handlingOption, $quota, $amount),
            PostItemTypeEnum::TYPE_NON_STATUTORY => $this->addNonStatutoryQuotaByHandlingOption($handlingOption, $quota, $amount),
            PostItemTypeEnum::TYPE_PARCEL => $this->addParcelQuotaByHandlingOption($handlingOption, $quota, $amount),
            default => throw new \InvalidArgumentException('Invalid post item type when getting quotas'),
        };
    }

    private function addGenericQuotaByHandlingOption(string $handlingOption, MailboxQuotas &$quota, int $amount = 1): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->setMailCollected($quota->getMailCollected() + $amount)
                ->getMailCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->setMailPosted($quota->getMailPosted() + $amount)
                ->getMailPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when adding generic quotas'),
        };
    }

    private function updateStatutoryQuotasByHandlingOption(string $handlingOption, MailboxQuotas &$quota, int $amount): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->setStatutoryCollected($amount)
                ->getStatutoryCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->setStatutoryPosted($amount)
                ->getStatutoryPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when updating statutory quotas'),
        };
    }

    private function updateNonStatutoryQuotasByHandlingOption(string $handlingOption, MailboxQuotas &$quota, int $amount): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->setNonStatutoryCollected($amount)
                ->getNonStatutoryCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->setNonStatutoryPosted($amount)
                ->getNonStatutoryPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when updating non-statutory quotas'),
        };
    }

    private function updateParcelQuotasByHandlingOption(string $handlingOption, MailboxQuotas &$quota, int $amount): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->setParcelCollected($amount)
                ->getParcelCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->setParcelPosted($amount)
                ->getParcelPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when updating parcel quotas'),
        };
    }

    private function updateQuotasByTypeAndHandlingOption(string $type, string $handlingOption, MailboxQuotas &$quota, int $amount): int
    {
        return match ($type) {
            PostItemTypeEnum::TYPE_STATUTORY->value => $this->updateStatutoryQuotasByHandlingOption($handlingOption, $quota, $amount),
            PostItemTypeEnum::TYPE_NON_STATUTORY->value => $this->updateNonStatutoryQuotasByHandlingOption($handlingOption, $quota, $amount),
            PostItemTypeEnum::TYPE_PARCEL->value => $this->updateParcelQuotasByHandlingOption($handlingOption, $quota, $amount),
            default => throw new \InvalidArgumentException('Invalid post item type when updating quotas'),
        };
    }

    private function updateGenericQuotasByHandlingOption(string $handlingOption, MailboxQuotas &$quota, int $amount): int
    {
        return match ($handlingOption) {
            MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
            MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT
            => $quota->setMailCollected($amount)
                ->getMailCollected(),
            MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            => $quota->setMailPosted($amount)
                ->getMailPosted(),
            default => throw new \InvalidArgumentException('Invalid handling option when updating generic quotas'),
        };
    }
}
