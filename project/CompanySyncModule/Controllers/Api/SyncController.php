<?php

namespace CompanySyncModule\Controllers\Api;

use CompanySyncModule\Services\SynchronizationService;
use Doctrine\ORM\EntityManager;
use <PERSON><PERSON>\Serializer\Exception\Exception as JMSException;
use Libs\CHFiling\Core\Exceptions\AuthenticationCodeExpiredException;
use Libs\CHFiling\Core\Exceptions\InvalidCompanyAuthenticationCodeException;
use RouterModule\ApiController;
use Services\CompanyService;
use Symfony\Component\HttpFoundation\JsonResponse;

class SyncController extends ApiController
{
    private const ERROR_ID_REQUIRED = 'Please provide companyId';
    private const ERROR_INVALID_COMPANY_ID = 'Invalid companyId (must be numeric)';
    private const ERROR_COMPANY_DOESNT_EXIST = "Invalid companyId (Company doesn't exist)";
    private const ERROR_COMPANY_NOT_INCORPORATED = 'Company is not incorporated';
    private const REGEX_COMPANY_ID = '/^[0-9]*$/';

    private const NOT_LOGGAGLE_ERROR_MESSAGES = [
        'Companies House are unable to complete this request',
        'Authentication code expired',
        'Premise cannot be longer than',
    ];

    /**
     * @var SynchronizationService
     */
    private $synchronizationService;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var EntityManager
     */
    private $em;

    public function __construct(
        EntityManager $em,
        SynchronizationService $synchronizationService,
        CompanyService $companyService,
    ) {
        $this->synchronizationService = $synchronizationService;
        $this->companyService = $companyService;
        $this->em = $em;
    }

    public function missingId()
    {
        return $this->apiErrorResponse(self::ERROR_ID_REQUIRED);
    }

    public function sync($companyId): JsonResponse
    {
        if (!preg_match(self::REGEX_COMPANY_ID, $companyId)) {
            return $this->apiErrorResponse(self::ERROR_INVALID_COMPANY_ID);
        }

        $this->em->getFilters()->disable('hiddenCompanies');

        $company = $this->companyService->getCompanyById($companyId);
        if (empty($company)) {
            return $this->apiErrorResponse(self::ERROR_COMPANY_DOESNT_EXIST);
        }

        if (!$company->isIncorporated()) {
            return $this->apiErrorResponse(self::ERROR_COMPANY_NOT_INCORPORATED);
        }

        if (!$company->hasAuthenticationCode()) {
            return $this->apiErrorResponse(
                error: 'Company does not have an authentication code',
                logError: false
            );
        }

        try {
            $this->synchronizationService->synchronizeCompany($company);

            return $this->apiSuccessResponse();
        } catch (JMSException|\Exception $e) {
            return $this->apiErrorResponse(error: $e->getMessage(), logError: $this->isLoggable($e));
        }
    }

    private function isLoggable(\Throwable $e): bool
    {
        if ($e instanceof InvalidCompanyAuthenticationCodeException || $e instanceof AuthenticationCodeExpiredException) {
            return false;
        }

        foreach (self::NOT_LOGGAGLE_ERROR_MESSAGES as $notLoggableMessage) {
            if (str_contains($e->getMessage(), $notLoggableMessage)) {
                return false;
            }
        }

        return true;
    }
}
